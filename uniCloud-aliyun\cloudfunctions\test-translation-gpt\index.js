// uniCloud云函数：测试GPT翻译功能并进行网络诊断
// 参考 subtitle-translation-gpt 结构，专门用于测试和诊断网络问题
"use strict";

// 常量配置 - 直接写死，不读取配置文件
const CONFIG = {
  // API配置
  API_KEY: "sk-OppCJ2Kc3gMqYc0jA79aA759Ec634c649361E90b79BfE72e",
  BASE_URL: "https://aihubmix.com",
  MODEL: "gpt-5-mini",

  // 请求配置
  TEMPERATURE: 0.3,
  MAX_TOKENS: 1000,
  API_TIMEOUT: 60000 * 3, // 3分钟超时，比原来更长
  TEST_TIMEOUT: 30000, // 测试用的较短超时

  // 测试内容
  TEST_TEXT: "Hello, this is a network connectivity test for the translation system.",
  TARGET_LANGUAGE: "zh"
};

/**
 * 云函数入口 - 无需任何入参，自动执行翻译测试和网络诊断
 * @param {Object} event - 事件参数（忽略）
 * @param {Object} context - 上下文
 * @returns {Object} 测试结果
 */
exports.main = async (event, context) => {
  const startTime = Date.now();
  console.log('🧪 开始翻译功能和网络诊断测试...');

  try {
    return await runSimpleTranslationTest(context);
  } catch (error) {
    console.error('❌ 测试失败:', error);
    return {
      success: false,
      error: error.message,
      duration: Date.now() - startTime
    };
  }
};

/**
 * 运行简单的翻译测试（包含网络诊断）
 */
async function runSimpleTranslationTest(context) {
  console.log('🔍 开始网络诊断和翻译测试...');

  const results = {
    timestamp: new Date().toISOString(),
    environment: {
      region: context.REGION || 'unknown',
      functionName: context.FUNCTION_NAME,
      requestId: context.REQUEST_ID,
      nodeVersion: process.version,
      platform: process.platform
    },
    tests: {}
  };

  // 1. DNS解析测试
  try {
    console.log('📡 步骤1: 测试DNS解析...');
    const dns = require('dns').promises;
    const dnsStart = Date.now();
    const addresses = await dns.resolve4('aihubmix.com');
    const dnsTime = Date.now() - dnsStart;

    results.tests.dns = {
      success: true,
      addresses,
      responseTime: dnsTime,
      message: `DNS解析成功: ${addresses.join(', ')} (${dnsTime}ms)`
    };
    console.log('✅ DNS解析成功:', addresses);
  } catch (dnsError) {
    results.tests.dns = {
      success: false,
      error: dnsError.message,
      message: `DNS解析失败: ${dnsError.message}`
    };
    console.error('❌ DNS解析失败:', dnsError.message);
  }

  // 2. HTTP连接测试
  try {
    console.log('📡 步骤2: 测试HTTP连接...');
    const httpStart = Date.now();
    const response = await uniCloud.httpclient.request('https://aihubmix.com', {
      method: 'GET',
      timeout: CONFIG.TEST_TIMEOUT,
      headers: {
        'User-Agent': 'uniCloud-test-translation/1.0'
      }
    });
    const httpTime = Date.now() - httpStart;

    results.tests.http = {
      success: true,
      statusCode: response.status,
      responseTime: httpTime,
      message: `HTTP连接成功，状态码: ${response.status} (${httpTime}ms)`
    };
    console.log('✅ HTTP连接成功:', response.status);
  } catch (httpError) {
    results.tests.http = {
      success: false,
      error: httpError.message,
      message: `HTTP连接失败: ${httpError.message}`
    };
    console.error('❌ HTTP连接失败:', httpError.message);
  }

  // 3. 翻译测试
  try {
    console.log('📡 步骤3: 测试翻译功能...');
    const translationStart = Date.now();

    console.log('🔑 使用固定API配置', {
      baseUrl: CONFIG.BASE_URL,
      model: CONFIG.MODEL,
      hasApiKey: !!CONFIG.API_KEY
    });

    // 执行翻译
    const translationResult = await performTranslation(
      CONFIG.TEST_TEXT,
      CONFIG.TARGET_LANGUAGE,
      CONFIG.API_KEY,
      CONFIG.BASE_URL,
      CONFIG.MODEL
    );
    const translationTime = Date.now() - translationStart;

    results.tests.translation = {
      success: true,
      originalText: CONFIG.TEST_TEXT,
      translatedText: translationResult.translatedText,
      responseTime: translationTime,
      model: CONFIG.MODEL,
      message: `翻译成功 (${translationTime}ms)`
    };
    console.log('✅ 翻译成功:', translationResult.translatedText);

  } catch (translationError) {
    results.tests.translation = {
      success: false,
      error: translationError.message,
      originalText: CONFIG.TEST_TEXT,
      message: `翻译失败: ${translationError.message}`
    };
    console.error('❌ 翻译失败:', translationError.message);
  }

  return {
    success: true,
    data: results,
    summary: generateSimpleSummary(results)
  };
}





/**
 * 执行翻译
 */
async function performTranslation(text, targetLanguage, apiKey, baseUrl, model) {
  const targetLangName = targetLanguage === 'zh' ? '中文' : '英文';
  
  const requestBody = {
    model,
    messages: [
      {
        role: "system",
        content: `你是一位专业的翻译专家。请将用户提供的文本翻译成${targetLangName}，要求准确、流畅、自然。`
      },
      {
        role: "user",
        content: `请将以下文本翻译成${targetLangName}：\n\n${text}`
      }
    ],
    temperature: CONFIG.TEMPERATURE,
    max_tokens: CONFIG.MAX_TOKENS,
  };

  console.log('📡 发送翻译请求', {
    model,
    textLength: text.length,
    targetLanguage: targetLangName,
    baseUrl
  });

  const response = await uniCloud.httpclient.request(`${baseUrl}/v1/chat/completions`, {
    method: "POST",
    headers: {
      Authorization: `Bearer ${apiKey}`,
      "Content-Type": "application/json",
    },
    data: JSON.stringify(requestBody),
    dataType: "json",
    timeout: CONFIG.API_TIMEOUT,
  });

  if (response.status !== 200) {
    throw new Error(`API请求失败，状态码: ${response.status}`);
  }

  const result = response.data;
  if (!result.choices || !result.choices[0] || !result.choices[0].message) {
    throw new Error("API响应格式异常");
  }

  const translatedText = result.choices[0].message.content.trim();
  
  return {
    translatedText,
    apiResponse: {
      model: result.model,
      usage: result.usage,
      finishReason: result.choices[0].finish_reason
    }
  };
}

/**
 * 生成简单测试摘要
 */
function generateSimpleSummary(results) {
  const tests = results.tests;
  const summary = {
    totalTests: Object.keys(tests).length,
    successfulTests: Object.values(tests).filter(t => t.success).length,
    issues: [],
    recommendations: []
  };

  if (!tests.dns?.success) {
    summary.issues.push('DNS解析失败');
    summary.recommendations.push('检查网络连接或使用备用域名');
  }

  if (!tests.http?.success) {
    summary.issues.push('HTTP连接失败');
    summary.recommendations.push('检查防火墙设置或网络策略');
  }

  if (!tests.translation?.success) {
    summary.issues.push('翻译功能失败');
    summary.recommendations.push('检查API密钥配置或服务状态');
  }

  if (summary.issues.length === 0) {
    summary.recommendations.push('所有测试通过，系统运行正常');
  }

  // 添加性能信息
  if (tests.translation?.success) {
    summary.performance = {
      translationTime: tests.translation.responseTime,
      textLength: CONFIG.TEST_TEXT.length,
      throughput: Math.round(CONFIG.TEST_TEXT.length / (tests.translation.responseTime / 1000))
    };
  }

  return summary;
}
